steps:
- name: 'gcr.io/cloud-builders/docker'
  args: ['build', '-t', 'southamerica-west1-docker.pkg.dev/gcp-cicd-demo-400000/pyapprepo-artifact/pyappimage:latest', '.']

- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'southamerica-west1-docker.pkg.dev/gcp-cicd-demo-400000/pyapprepo-artifact/pyappimage:latest']

- name: 'gcr.io/cloud-builders/gcloud'
  args: ['run', 'deploy', 'pyapp', '--image', 'southamerica-west1-docker.pkg.dev/gcp-cicd-demo-400000/pyapprepo-artifact/pyappimage:latest', '--region', 'southamerica-west1', '--platform', 'managed', '--allow-unauthenticated']