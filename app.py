from flask import Flask
import os

# Configuración minimalista de Flask
app = Flask(__name__)
app.config['JSON_SORT_KEYS'] = False

@app.route('/')
def wish():
    """Endpoint principal que retorna un saludo de cumpleaños personalizado."""
    message = "Happy Birthday {name}"
    return message.format(name=os.getenv('NAM<PERSON>', "<PERSON>"))

@app.route('/health')
def health_check():
    """Health check endpoint para Cloud Run."""
    return {'status': 'healthy', 'service': 'pyflaskapp'}

if __name__ == '__main__':
    # Configuración optimizada para Cloud Run
    app.run(
        host='0.0.0.0',
        port=int(os.getenv('PORT', 8080)),
        debug=False,
        threaded=True
    )