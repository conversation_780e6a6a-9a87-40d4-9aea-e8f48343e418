from flask import Flask
import os

app = Flask(__name__)

@app.route('/')
def wish():
    """Endpoint principal que retorna un saludo de cumpleaños personalizado."""
    message = "Happy Birthday {name}"
    return message.format(name=os.getenv('NAME', "<PERSON>"))

@app.route('/health')
def health_check():
    """Health check endpoint para Cloud Run."""
    return {'status': 'healthy', 'service': 'pyflaskapp'}, 200

if __name__ == '__main__':
    # Solo para desarrollo local
    app.run(host='0.0.0.0', port=8080, debug=False)