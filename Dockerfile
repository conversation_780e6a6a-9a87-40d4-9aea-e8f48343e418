# Usar imagen Alpine para menor tamaño y menos vulnerabilidades
FROM python:3.12-alpine

# Instalar dependencias mínimas del sistema
RUN apk add --no-cache \
    ca-certificates \
    && addgroup -g 1000 appuser \
    && adduser -D -u 1000 -G appuser appuser

# Configurar directorio de trabajo
WORKDIR /app

# Copiar y instalar dependencias Python
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copiar código de aplicación
COPY app.py .

# Cambiar ownership y usuario
RUN chown -R appuser:appuser /app
USER appuser

# Variables de entorno optimizadas
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    NAME=Mark

# Exponer puerto
EXPOSE 8080

# Comando simplificado - usar Flask development server optimizado
CMD ["python", "app.py"]