# Usar imagen base más reciente y segura con tag específico
FROM python:3.12.7-slim

# Crear usuario no-root para seguridad con UID/GID específicos
RUN groupadd -r appuser --gid=1000 && \
    useradd -r -g appuser --uid=1000 --home-dir=/app --shell=/bin/bash appuser

# Actualizar sistema y instalar dependencias de seguridad mínimas
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
        ca-certificates \
        && \
    apt-get autoremove -y && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Configurar directorio de trabajo
WORKDIR /app

# Crear requirements.txt para mejor gestión de dependencias
COPY requirements.txt .

# Instalar dependencias Python de forma segura
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copiar código de aplicación y configuración
COPY app.py .
COPY gunicorn.conf.py .

# Cambiar ownership al usuario no-root
RUN chown -R appuser:appuser /app

# Cambiar a usuario no-root
USER appuser

# Variables de entorno
ENV NAME=Mark
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Exponer puerto
EXPOSE 8080

# Comando de inicio con Gunicorn usando archivo de configuración
CMD ["gunicorn", "--config", "gunicorn.conf.py", "app:app"]