steps:
- name: 'gcr.io/cloud-builders/docker'
  args: ['build', '-t', 'southamerica-west1-docker.pkg.dev/gcp-cicd-demo-400000/pyapprepo-artifact/pyappimage:latest', '.']

- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'southamerica-west1-docker.pkg.dev/gcp-cicd-demo-400000/pyapprepo-artifact/pyappimage:latest']

- name: 'gcr.io/cloud-builders/gcloud'
  args: ['run', 'deploy', 'pyflaskapp', '--image', 'southamerica-west1-docker.pkg.dev/gcp-cicd-demo-400000/pyapprepo-artifact/pyappimage:latest', '--region', 'southamerica-west1', '--platform', 'managed', '--allow-unauthenticated']

# Configuración de logging para resolver el error de service_account
options:
  logging: CLOUD_LOGGING_ONLY