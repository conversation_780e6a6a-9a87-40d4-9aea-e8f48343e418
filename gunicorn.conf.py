# Configuración de Gunicorn para producción segura
import multiprocessing
import os

# Configuración del servidor
bind = "0.0.0.0:8080"
workers = min(2, multiprocessing.cpu_count())
worker_class = "sync"
worker_connections = 1000

# Configuración de timeouts
timeout = 30
keepalive = 2
graceful_timeout = 30

# Configuración de requests
max_requests = 1000
max_requests_jitter = 100

# Configuración de memoria
preload_app = True

# Configuración de logging
accesslog = "-"
errorlog = "-"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Configuración de seguridad
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# Configuración de proceso
user = 1000
group = 1000
tmp_upload_dir = None

# Configuración de SSL (si se necesita en el futuro)
# keyfile = None
# certfile = None
